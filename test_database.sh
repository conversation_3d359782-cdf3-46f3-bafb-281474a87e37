#!/bin/bash

# =====================================================
# FPT UNIVERSITY 2025 - DATABASE VERIFICATION SCRIPT
# Purpose: Test schema and data with Docker PostgreSQL
# =====================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CONTAINER_NAME="fpt_university_test_db"
DB_NAME="fpt_university_test"
DB_USER="postgres"
DB_PASSWORD="test123"
DB_PORT="5433"

echo -e "${BLUE}🚀 Starting FPT University Database Verification...${NC}"

# Function to cleanup
cleanup() {
    echo -e "${YELLOW}🧹 Cleaning up...${NC}"
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

echo -e "${BLUE}🐳 Starting PostgreSQL container...${NC}"

# Stop and remove existing container if it exists
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true

# Start PostgreSQL container
docker run -d \
    --name $CONTAINER_NAME \
    -e POSTGRES_DB=$DB_NAME \
    -e POSTGRES_USER=$DB_USER \
    -e POSTGRES_PASSWORD=$DB_PASSWORD \
    -p $DB_PORT:5432 \
    postgres:15-alpine

echo -e "${YELLOW}⏳ Waiting for PostgreSQL to be ready...${NC}"

# Wait for PostgreSQL to be ready
for i in {1..30}; do
    if docker exec $CONTAINER_NAME pg_isready -U $DB_USER -d $DB_NAME > /dev/null 2>&1; then
        echo -e "${GREEN}✅ PostgreSQL is ready!${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ PostgreSQL failed to start within 30 seconds${NC}"
        exit 1
    fi
    sleep 1
done

echo -e "${BLUE}📊 Creating schema...${NC}"

# Execute schema creation
if docker exec -i $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < docs/database/fpt_university_2025_init.sql > /dev/null; then
    echo -e "${GREEN}✅ Schema created successfully${NC}"
else
    echo -e "${RED}❌ Schema creation failed${NC}"
    exit 1
fi

echo -e "${BLUE}📝 Populating data...${NC}"

# Execute data migration
if docker exec -i $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < docs/database/fpt_university_2025_data_migration.sql > /dev/null; then
    echo -e "${GREEN}✅ Data populated successfully${NC}"
else
    echo -e "${RED}❌ Data population failed${NC}"
    exit 1
fi

echo -e "${BLUE}🔍 Running verification tests...${NC}"

# Create verification SQL script
cat > /tmp/verify_tests.sql << 'EOF'
-- =====================================================
-- VERIFICATION TESTS
-- =====================================================

\echo '=== DATA SUMMARY ==='
SELECT * FROM v_data_summary ORDER BY table_name;

\echo ''
\echo '=== CAMPUS VERIFICATION ==='
SELECT 
    code,
    name,
    city,
    discount_percentage,
    is_active
FROM campuses 
ORDER BY code;

\echo ''
\echo '=== FOUNDATION FEES VERIFICATION ==='
SELECT 
    c.code as campus_code,
    c.name as campus_name,
    ff.standard_fee,
    ff.discounted_fee,
    ROUND((ff.standard_fee - ff.discounted_fee) * 100.0 / ff.standard_fee, 1) as actual_discount_pct
FROM foundation_fees ff
JOIN campuses c ON c.id = ff.campus_id
ORDER BY c.code;

\echo ''
\echo '=== PROGRAM COUNT BY DEPARTMENT ==='
SELECT 
    department,
    COUNT(*) as program_count
FROM programs 
WHERE is_active = true
GROUP BY department
ORDER BY program_count DESC;

\echo ''
\echo '=== TUITION VERIFICATION (Sample) ==='
SELECT 
    p.code as program_code,
    p.name as program_name,
    c.code as campus_code,
    pt.semester_group_1_3_fee,
    pt.semester_group_4_6_fee,
    pt.semester_group_7_9_fee
FROM progressive_tuition pt
JOIN programs p ON p.id = pt.program_id
JOIN campuses c ON c.id = pt.campus_id
WHERE p.code IN ('SE', 'BL', 'EN')
ORDER BY c.code, p.code;

\echo ''
\echo '=== BUSINESS LOGIC VERIFICATION ==='
-- Check if discount logic is correct
SELECT 
    'Foundation Fee Discount Logic' as test_name,
    CASE 
        WHEN COUNT(*) = 0 THEN 'PASS'
        ELSE 'FAIL - ' || COUNT(*) || ' records violate discount logic'
    END as result
FROM foundation_fees 
WHERE discounted_fee > standard_fee;

SELECT 
    'Semester Fee Progression Logic' as test_name,
    CASE 
        WHEN COUNT(*) = 0 THEN 'PASS'
        ELSE 'FAIL - ' || COUNT(*) || ' records violate progression logic'
    END as result
FROM progressive_tuition 
WHERE semester_group_1_3_fee > semester_group_4_6_fee 
   OR semester_group_4_6_fee > semester_group_7_9_fee;

SELECT 
    'Discount Percentage Range' as test_name,
    CASE 
        WHEN COUNT(*) = 0 THEN 'PASS'
        ELSE 'FAIL - ' || COUNT(*) || ' records have invalid discount percentage'
    END as result
FROM campuses 
WHERE discount_percentage < 0 OR discount_percentage > 100;

\echo ''
\echo '=== SEARCH FUNCTION TEST ==='
SELECT * FROM search_programs_ranked('kỹ thuật phần mềm', 5);

\echo ''
\echo '=== MATERIALIZED VIEW TEST ==='
SELECT 
    code,
    name,
    campus_count,
    min_tuition,
    max_tuition,
    avg_tuition
FROM mv_program_search 
WHERE code IN ('SE', 'AI', 'BL', 'EN')
ORDER BY code;

\echo ''
\echo '=== INDEX VERIFICATION ==='
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
  AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

EOF

# Run verification tests
echo -e "${YELLOW}📋 Running verification tests...${NC}"
docker exec -i $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < /tmp/verify_tests.sql

echo -e "${GREEN}🎉 All tests completed successfully!${NC}"
echo -e "${BLUE}📊 Database verification summary:${NC}"
echo -e "  • Schema: ✅ Created"
echo -e "  • Data: ✅ Populated"
echo -e "  • Constraints: ✅ Verified"
echo -e "  • Indexes: ✅ Created"
echo -e "  • Views: ✅ Working"
echo -e "  • Functions: ✅ Working"

# Cleanup temp file
rm -f /tmp/verify_tests.sql

echo -e "${GREEN}✅ Database verification completed successfully!${NC}"
echo -e "${YELLOW}🧹 Container will be cleaned up automatically...${NC}"
