-- =====================================================
-- FPT UNIVERSITY 2025 - COMBINED SCHEMA V2 (Production Ready)
-- Version: 1.3.0 - UUID Standard + Manual updated_at + Critical Optimizations
-- Date: 2025-01-27
-- Changes: Added foreign key indexes, data integrity constraints, enhanced monitoring
-- =====================================================

-- ✅ ENABLE EXTENSIONS
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS btree_gist;

-- ✅ BASE SCHEMA: TABLES
CREATE TABLE programs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    department VARCHAR(100) NOT NULL,
    duration_years INTEGER NOT NULL DEFAULT 4,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE campuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    city VARCHAR(100) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    foundation_fee DECIMAL(15,2) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE progressive_tuition (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    program_id UUID NOT NULL REFERENCES programs(id),
    campus_id UUID NOT NULL REFERENCES campuses(id),
    year INTEGER NOT NULL,
    tier_1_fee DECIMAL(15,2) NOT NULL,
    tier_2_fee DECIMAL(15,2) NOT NULL,
    tier_3_fee DECIMAL(15,2) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(program_id, campus_id, year)
);

CREATE TABLE scholarships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    recipients INTEGER,
    percentage DECIMAL(5,2),
    requirements TEXT,
    year INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE admission_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    method_code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    requirements TEXT,
    year INTEGER NOT NULL DEFAULT 2025,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE program_campus_availability (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    program_id UUID NOT NULL REFERENCES programs(id),
    campus_id UUID NOT NULL REFERENCES campuses(id),
    is_available BOOLEAN DEFAULT true,
    year INTEGER NOT NULL DEFAULT 2025,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(program_id, campus_id, year)
);

-- ✅ VIEW: v_progressive_tuition
CREATE OR REPLACE VIEW v_progressive_tuition AS
SELECT
    pt.id,
    pt.program_id,
    p.code AS program_code,
    p.name AS program_name,
    pt.year,
    pt.tier_1_fee,
    pt.tier_2_fee,
    pt.tier_3_fee,
    c.id AS campus_id,
    c.code AS campus_code,
    c.name AS campus_name,
    c.foundation_fee,
    c.discount_percentage,
    pt.is_active,
    pt.created_at
FROM progressive_tuition pt
JOIN campuses c ON pt.campus_id = c.id
JOIN programs p ON pt.program_id = p.id
WHERE pt.is_active = true AND c.is_active = true AND p.is_active = true;

-- ✅ MATERIALIZED VIEW: mv_program_search
CREATE MATERIALIZED VIEW mv_program_search AS
SELECT
    p.id,
    p.code,
    p.name,
    p.name_en,
    p.department,
    p.duration_years,
    string_agg(DISTINCT c.code || ':' || c.name, '; ') as campus_info,
    string_agg(DISTINCT c.code, ', ') as campus_codes,
    COUNT(DISTINCT c.id) as campus_count,
    MIN(pt.tier_1_fee) as min_tuition,
    MAX(pt.tier_3_fee) as max_tuition,
    AVG((pt.tier_1_fee + pt.tier_2_fee + pt.tier_3_fee) / 3)::DECIMAL(15,2) as avg_tuition,
    p.code || ' ' || p.name || ' ' || COALESCE(p.name_en, '') || ' ' || p.department as search_text
FROM programs p
LEFT JOIN program_campus_availability pca ON pca.program_id = p.id AND pca.is_available = true
LEFT JOIN campuses c ON c.id = pca.campus_id AND c.is_active = true
LEFT JOIN progressive_tuition pt ON pt.program_id = p.id AND pt.is_active = true
WHERE p.is_active = true
GROUP BY p.id, p.code, p.name, p.name_en, p.department, p.duration_years;

-- ✅ CRITICAL INDEXES: Foreign Keys (Performance Critical)
CREATE INDEX idx_progressive_tuition_program_id ON progressive_tuition(program_id);
CREATE INDEX idx_progressive_tuition_campus_id ON progressive_tuition(campus_id);
CREATE INDEX idx_program_campus_program_id ON program_campus_availability(program_id);
CREATE INDEX idx_program_campus_campus_id ON program_campus_availability(campus_id);

-- ✅ QUERY PERFORMANCE INDEXES
CREATE INDEX idx_programs_department ON programs(department);
CREATE INDEX idx_progressive_tuition_year ON progressive_tuition(year);
CREATE INDEX idx_scholarships_year ON scholarships(year);

-- ✅ INDEXES FOR MATERIALIZED VIEWS
CREATE INDEX idx_mv_program_search_code ON mv_program_search(code);
CREATE INDEX idx_mv_program_search_text ON mv_program_search USING gin(search_text gin_trgm_ops);

-- ✅ FUNCTION: search_programs_ranked
CREATE OR REPLACE FUNCTION search_programs_ranked(
    search_text VARCHAR,
    limit_results INTEGER DEFAULT 20
) RETURNS TABLE (
    code VARCHAR,
    name VARCHAR,
    name_en VARCHAR,
    department VARCHAR,
    campuses TEXT,
    min_tuition DECIMAL,
    max_tuition DECIMAL,
    relevance_score REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        mv.code,
        mv.name,
        mv.name_en,
        mv.department,
        mv.campus_codes,
        mv.min_tuition,
        mv.max_tuition,
        CASE
            WHEN LOWER(mv.code) = LOWER(search_text) THEN 1.0
            WHEN LOWER(mv.name) = LOWER(search_text) THEN 0.9
            WHEN LOWER(mv.name_en) = LOWER(search_text) THEN 0.9
            ELSE similarity(mv.search_text, search_text)
        END as relevance_score
    FROM mv_program_search mv
    WHERE
        similarity(mv.search_text, search_text) > 0.1
        OR LOWER(mv.code) = LOWER(search_text)
        OR LOWER(mv.search_text) LIKE LOWER('%' || search_text || '%')
    ORDER BY
        relevance_score DESC,
        mv.name
    LIMIT limit_results;
END;
$$ LANGUAGE plpgsql;

-- ✅ CALL TO REFRESH MATERIALIZED VIEWS
CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_program_search;
END;
$$ LANGUAGE plpgsql;

-- ✅ DATA INTEGRITY CONSTRAINTS (Critical for Data Protection)
ALTER TABLE progressive_tuition ADD CONSTRAINT chk_fees_positive
    CHECK (tier_1_fee > 0 AND tier_2_fee > 0 AND tier_3_fee > 0);

ALTER TABLE progressive_tuition ADD CONSTRAINT chk_tier_progression
    CHECK (tier_1_fee <= tier_2_fee AND tier_2_fee <= tier_3_fee);

ALTER TABLE campuses ADD CONSTRAINT chk_discount_valid
    CHECK (discount_percentage >= 0 AND discount_percentage <= 100);

ALTER TABLE campuses ADD CONSTRAINT chk_foundation_fee_positive
    CHECK (foundation_fee > 0);

ALTER TABLE scholarships ADD CONSTRAINT chk_percentage_valid
    CHECK (percentage >= 0 AND percentage <= 100);

ALTER TABLE programs ADD CONSTRAINT chk_duration_valid
    CHECK (duration_years BETWEEN 1 AND 6);

ALTER TABLE progressive_tuition ADD CONSTRAINT chk_year_valid
    CHECK (year >= 2020 AND year <= 2035);

ALTER TABLE admission_methods ADD CONSTRAINT chk_year_valid_admission
    CHECK (year >= 2020 AND year <= 2035);

-- ✅ MONITORING
CREATE VIEW v_table_sizes AS
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- ✅ ENHANCED MONITORING VIEW
CREATE VIEW v_data_summary AS
SELECT
    'programs' as table_name,
    COUNT(*) as total_records,
    COUNT(*) FILTER (WHERE is_active = true) as active_records,
    MAX(created_at) as latest_created
FROM programs
UNION ALL
SELECT 'campuses', COUNT(*), COUNT(*) FILTER (WHERE is_active = true), MAX(created_at) FROM campuses
UNION ALL
SELECT 'scholarships', COUNT(*), COUNT(*) FILTER (WHERE is_active = true), MAX(created_at) FROM scholarships
UNION ALL
SELECT 'progressive_tuition', COUNT(*), COUNT(*) FILTER (WHERE is_active = true), MAX(created_at) FROM progressive_tuition
UNION ALL
SELECT 'admission_methods', COUNT(*), COUNT(*) FILTER (WHERE is_active = true), MAX(created_at) FROM admission_methods;

-- ✅ DONE: Production-ready schema with manual updated_at management in backend
-- Version: 1.3.0 - Added critical indexes and data integrity constraints
