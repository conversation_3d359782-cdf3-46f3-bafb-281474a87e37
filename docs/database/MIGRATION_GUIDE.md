# Migration Guide: Enterprise → MVP

## Overview
This guide helps migrate from the enterprise database (`fpt_university_2025_official.sql`) to the simplified MVP version (`fpt_university_2025_mvp.sql`).

## What Changed?

### ❌ Removed Features
- Alias tables (`program_aliases`, `campus_aliases`)
- Audit logs system
- Cache management tables
- Rate limiting
- Performance monitoring
- Materialized views
- Soft delete columns

### ✅ Kept Features
- All core data tables
- Essential views
- Basic search functions
- Complete 2025 official data

## Migration Steps

### 1. Backup Current Database
```bash
pg_dump -d fpt_university > backup_enterprise.sql
```

### 2. Create New MVP Database
```bash
createdb fpt_university_mvp
psql -d fpt_university_mvp -f fpt_university_2025_mvp.sql
```

### 3. Migrate Custom Data (if any)
```sql
-- If you have custom data, migrate it:
-- Example: Copy custom program entries
INSERT INTO fpt_university_mvp.programs 
SELECT id, code, name, name_en, department, duration_years, is_active, created_at
FROM fpt_university.programs
WHERE created_at > '2025-01-01'; -- Custom entries only
```

### 4. Update Application Code

#### Remove alias-based searches:
```javascript
// OLD (with aliases)
const result = await db.query('SELECT * FROM search_programs_by_name($1)', ['KTPM']);

// NEW (direct search)
const result = await db.query('SELECT * FROM search_programs($1)', ['Kỹ thuật phần mềm']);
```

#### Remove cache functions:
```javascript
// OLD
await db.query('SELECT cache_set($1, $2)', [key, value]);

// NEW - Use application cache
cache.set(key, value, ttl);
```

## Compatibility Notes

- MVP uses same core table structures
- Primary keys (UUID) remain compatible
- All official 2025 data is preserved
- Views simplified but same output format

## Need Enterprise Features?

If you need specific enterprise features:
1. Check archived version in `docs/archive/old-versions/`
2. Extract only needed features
3. Add to MVP incrementally

## Questions?
The MVP version is designed for simplicity and maintainability. Most "missing" features can be handled at the application layer. 