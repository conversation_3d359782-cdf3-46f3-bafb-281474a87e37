# Intent to Query Mapping Guide

This guide maps user intents from `intent-examples.json` to corresponding SQL queries in the MVP database.

## 🎯 Quick Reference

| Intent | Database Support | Query Type | RAG Needed |
|--------|-----------------|------------|------------|
| campus_info | ✅ Partial | Direct Query | Yes (facilities) |
| tuition_inquiry | ✅ Full | Function Call | No |
| program_search | ✅ Full | Search Function | No |
| scholarship_inquiry | ✅ Full | Direct Query | No |
| admission_process | ❌ | - | Yes |
| contact_information | ✅ Full | Direct Query | No |
| deadline_inquiry | ✅ Partial | Direct Query | Yes (details) |

## 📊 Detailed Mapping

### 1. Campus Information (`campus_info`)

**User asks:** "FPT có mấy campus?", "Campus Hà Nội ở đâu?"

```sql
-- Count campuses
SELECT COUNT(*) FROM campuses WHERE is_active = true;

-- Get campus details
SELECT * FROM campuses WHERE code = 'HN';

-- List all campuses
SELECT code, name, city, foundation_fee, discount_percentage 
FROM campuses ORDER BY code;
```

**Note:** Facility details (labs, library hours, etc.) need RAG support.

### 2. Tuition Inquiry (`tuition_inquiry`)

**User asks:** "Học phí ngành AI ở Hà Nội?", "Chi phí học SE 4 năm?"

```sql
-- Get specific tuition
SELECT * FROM get_tuition_info('AI', 'HN', 2025);

-- Compare across campuses
SELECT * FROM v_tuition_details 
WHERE program_code = 'AI' 
ORDER BY total_with_foundation;

-- Budget analysis
SELECT program_name, campus_name,
       total_with_foundation,
       ROUND(total_with_foundation / 36, 0) as monthly_payment_36m
FROM v_tuition_details
WHERE program_code = 'SE';
```

### 3. Program Search (`program_search`)

**User asks:** "FPT có ngành gì?", "Ngành IT có những chuyên ngành nào?"

```sql
-- Search by keyword
SELECT * FROM search_programs('phần mềm');

-- List by department
SELECT * FROM programs 
WHERE department = 'Công nghệ thông tin';

-- Check availability at campus
SELECT p.code, p.name FROM programs p
JOIN program_campus_availability pca ON p.id = pca.program_id
JOIN campuses c ON c.id = pca.campus_id
WHERE c.code = 'QN' AND pca.is_available = true;
```

### 4. Scholarship Inquiry (`scholarship_inquiry`)

**User asks:** "Học bổng 100% có thật không?", "Điều kiện học bổng?"

```sql
-- Get all scholarships
SELECT * FROM scholarships WHERE year = 2025;

-- Find full scholarships
SELECT * FROM scholarships 
WHERE percentage = 100 AND year = 2025;

-- Total scholarship slots
SELECT SUM(recipients) as total_slots 
FROM scholarships WHERE year = 2025;
```

### 5. Contact Information (`contact_information`)

**User asks:** "Hotline FPT?", "Email tuyển sinh Hà Nội?"

```sql
-- Get all contacts
SELECT code, name, phone, email 
FROM campuses ORDER BY code;

-- Specific campus contact
SELECT phone, email, address 
FROM campuses WHERE code = 'HN';
```

### 6. Admission Methods (`deadline_inquiry` partial)

**User asks:** "Cách nộp hồ sơ?", "Xét học bạ cần gì?"

```sql
-- Get admission methods
SELECT * FROM admission_methods 
WHERE year = 2025;

-- Specific method details
SELECT requirements FROM admission_methods 
WHERE method_code = 'M1';
```

## 🔧 Implementation Examples

### Handle Complex Tuition Questions

```javascript
async function handleTuitionInquiry(program, campus) {
  // Direct query for exact match
  if (program && campus) {
    return await db.query(
      'SELECT * FROM get_tuition_info($1, $2, $3)',
      [program, campus, 2025]
    );
  }
  
  // Search if partial info
  if (program) {
    return await db.query(
      'SELECT * FROM v_tuition_details WHERE program_code = $1',
      [program]
    );
  }
  
  // RAG for complex questions
  return await rag.search('học phí ' + program);
}
```

### Handle Program Search with Fallback

```javascript
async function searchPrograms(query) {
  // Try database first
  const dbResults = await db.query(
    'SELECT * FROM search_programs($1)',
    [query]
  );
  
  if (dbResults.length === 0) {
    // Fallback to RAG for aliases
    const ragResults = await rag.search(query);
    // Map RAG results to program codes
    // Then query database again
  }
  
  return dbResults;
}
```

### Budget Calculator Example

```javascript
async function calculateBudget(program, campus, months) {
  const result = await db.query(`
    SELECT 
      program_name,
      campus_name,
      total_with_foundation,
      ROUND(total_with_foundation / $1, 0) as monthly_payment
    FROM v_tuition_details
    WHERE program_code = $2 AND campus_code = $3
  `, [months, program, campus]);
  
  return result[0];
}
```

## 📝 Intent Routing Strategy

### Database-First Intents
These can be fully handled by database:
- `tuition_inquiry` → `get_tuition_info()`
- `program_search` → `search_programs()`
- `scholarship_inquiry` → Direct queries
- `contact_information` → Campus table

### Hybrid Intents
Need both database and RAG:
- `campus_info` → Basic info from DB, details from RAG
- `program_requirements` → Programs from DB, requirements from RAG
- `deadline_inquiry` → Methods from DB, dates from RAG

### RAG-Only Intents
No database support needed:
- `admission_process`
- `career_guidance`
- `student_activities`
- `international_programs`
- `ojt_internship`
- `alumni_success`

## 🚀 Best Practices

1. **Always try database first** for structured data
2. **Use RAG for context** and detailed explanations
3. **Combine results** for comprehensive answers
4. **Cache common queries** at application level
5. **Log failed queries** to improve system

## 💡 Query Optimization Tips

```sql
-- Use views for complex joins
SELECT * FROM v_tuition_details WHERE program_code = 'AI';

-- Use functions for common operations
SELECT * FROM get_tuition_info('AI', 'HN', 2025);

-- Index-friendly queries
SELECT * FROM programs WHERE code = 'AI'; -- Uses idx_programs_code

-- Avoid expensive operations
-- BAD: SELECT * FROM programs WHERE LOWER(name) LIKE '%ai%';
-- GOOD: SELECT * FROM search_programs('AI');
``` 