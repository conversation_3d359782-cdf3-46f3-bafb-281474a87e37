-- =====================================================
-- FPT UNIVERSITY 2025 - OFFICIAL DATABASE SCHEMA
-- Version: 2.1 Official (Added Aliases Support)
-- Date: 2025
-- Description: Complete schema with official data + alias support
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- DROP EXISTING TABLES (for clean setup)
-- =====================================================
DROP TABLE IF EXISTS progressive_tuition CASCADE;
DROP TABLE IF EXISTS scholarships CASCADE;
DROP TABLE IF EXISTS programs CASCADE;
DROP TABLE IF EXISTS campuses CASCADE;
DROP TABLE IF EXISTS program_aliases CASCADE;
DROP TABLE IF EXISTS campus_aliases CASCADE;
DROP TABLE IF EXISTS admission_methods CASCADE;
DROP TABLE IF EXISTS admission_quotas CASCADE;
DROP TABLE IF EXISTS program_campus_availability CASCADE;

-- =====================================================
-- 1. PROGRAMS TABLE (Complete Official List with Aliases)
-- =====================================================
CREATE TABLE programs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    name_short VARCHAR(100), -- Tên viết tắt
    aliases TEXT[], -- Mảng các tên gọi khác
    keywords TEXT[], -- Từ khóa tìm kiếm
    department VARCHAR(100) NOT NULL,
    duration_years INTEGER NOT NULL DEFAULT 4,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. CAMPUSES TABLE (Official Contact Info with Aliases)
-- =====================================================
CREATE TABLE campuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_short VARCHAR(100), -- Tên viết tắt
    aliases TEXT[], -- Các tên gọi khác
    city VARCHAR(100) NOT NULL,
    city_aliases TEXT[], -- Các cách gọi thành phố khác
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    foundation_fee DECIMAL(15,2) NOT NULL, -- Campus-specific foundation
    discount_percentage DECIMAL(5,2) DEFAULT 0, -- Official discounts
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    virtual_tour_url VARCHAR(500),
    description TEXT,
    facilities TEXT[],
    accommodations_capacity INTEGER
);

-- =====================================================
-- 3. PROGRAM_ALIASES TABLE (Flexible alias management)
-- =====================================================
CREATE TABLE program_aliases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    program_id UUID NOT NULL REFERENCES programs(id) ON DELETE CASCADE,
    alias_name VARCHAR(255) NOT NULL,
    alias_type VARCHAR(50) NOT NULL, -- 'common', 'abbreviation', 'informal', 'industry'
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 4. CAMPUS_ALIASES TABLE (Flexible campus alias management)
-- =====================================================
CREATE TABLE campus_aliases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campus_id UUID NOT NULL REFERENCES campuses(id) ON DELETE CASCADE,
    alias_name VARCHAR(255) NOT NULL,
    alias_type VARCHAR(50) NOT NULL, -- 'city', 'region', 'common', 'abbreviation'
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 5. PROGRESSIVE_TUITION TABLE (3-Tier Official Pricing)
-- =====================================================
CREATE TABLE progressive_tuition (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    program_id UUID NOT NULL REFERENCES programs(id),
    year INTEGER NOT NULL,
    
    -- Official 3-tier pricing từ tài liệu
    tier_1_fee DECIMAL(15,2) NOT NULL, -- Học kỳ 1,2,3
    tier_2_fee DECIMAL(15,2) NOT NULL, -- Học kỳ 4,5,6  
    tier_3_fee DECIMAL(15,2) NOT NULL, -- Học kỳ 7,8,9
    
    campus_code VARCHAR(10) NOT NULL, -- HN, HCM = base price
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(program_id, year, campus_code)
);

-- =====================================================
-- 6. SCHOLARSHIPS TABLE (Official 2025 Programs)
-- =====================================================
CREATE TABLE scholarships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_aliases TEXT[], -- Các cách gọi khác của học bổng
    type VARCHAR(50) NOT NULL, -- global_expert, school_path, full, partial_2year, partial_1year
    recipients INTEGER, -- 100, 900, 300, 500, 1000
    percentage DECIMAL(5,2), -- % giảm học phí
    requirements TEXT,
    year INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 7. ADMISSION_METHODS TABLE (Official 2025 Methods)
-- =====================================================
CREATE TABLE admission_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    method_code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_aliases TEXT[], -- Alternative names
    requirements TEXT,
    threshold_description TEXT,
    fees DECIMAL(10,2) DEFAULT 200000, -- 200k VND
    year INTEGER NOT NULL DEFAULT 2025,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 8. ADMISSION_QUOTAS TABLE (2025 Quotas)
-- =====================================================
CREATE TABLE admission_quotas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    program_id UUID NOT NULL REFERENCES programs(id),
    campus_id UUID NOT NULL REFERENCES campuses(id),
    total_quota INTEGER NOT NULL,
    year INTEGER NOT NULL DEFAULT 2025,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(program_id, campus_id, year)
);

-- =====================================================
-- 9. PROGRAM_CAMPUS_AVAILABILITY TABLE
-- =====================================================
CREATE TABLE program_campus_availability (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    program_id UUID NOT NULL REFERENCES programs(id),
    campus_id UUID NOT NULL REFERENCES campuses(id),
    is_available BOOLEAN DEFAULT true,
    special_notes TEXT, -- Special conditions per campus
    year INTEGER NOT NULL DEFAULT 2025,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(program_id, campus_id, year)
);

-- =====================================================
-- INSERT DATA - OFFICIAL CAMPUSES WITH ALIASES
-- =====================================================
INSERT INTO campuses (code, name, name_short, aliases, city, city_aliases, address, phone, email, foundation_fee, discount_percentage) VALUES
('HN', 'FPT University Hà Nội', 'FPTU Hà Nội', 
 ARRAY['FPT Hà Nội', 'FPTU HN', 'FPT HN', 'Đại học FPT Hà Nội', 'FPT Hòa Lạc', 'FPTU Hòa Lạc'], 
 'Hà Nội', ARRAY['HN', 'Hanoi', 'Ha Noi', 'Thủ đô', 'Hòa Lạc'],
 'Khu Giáo dục và Đào tạo -- Khu Công nghệ cao Hòa Lạc -- Km29 Đại lộ Thăng Long, H. Thạch Thất, TP. Hà Nội', 
 '024 7300 5588', '<EMAIL>', 13100000, 0.0),

('HCM', 'FPT University TP.HCM', 'FPTU TP.HCM', 
 ARRAY['FPT Sài Gòn', 'FPTU HCM', 'FPT HCM', 'Đại học FPT TP.HCM', 'FPT Sài Gòn', 'FPTU SGN', 'FPT Thủ Đức'], 
 'TP.HCM', ARRAY['HCM', 'Sài Gòn', 'TPHCM', 'SG', 'SGN', 'Thành phố Hồ Chí Minh', 'Thủ Đức'],
 'Lô E2a-7, Đường D1, Đ. D1, Long Thạnh Mỹ, Thành Phố Thủ Đức, Thành phố Hồ Chí Minh', 
 '028 7300 5588', '<EMAIL>', 13100000, 0.0),

('DN', 'FPT University Đà Nẵng', 'FPTU Đà Nẵng', 
 ARRAY['FPT Đà Nẵng', 'FPTU DN', 'FPT DN', 'Đại học FPT Đà Nẵng', 'FPT City Đà Nẵng'], 
 'Đà Nẵng', ARRAY['DN', 'Da Nang', 'Đà Nẵng', 'Miền Trung'],
 'Khu đô thị FPT City, ngõ 22 Thành Thái, P. Dĩ An, Q. Hải Châu, TP. Đà Nẵng', 
 '0236 730 0999', '<EMAIL>', 9170000, -30.0),

('CT', 'FPT University Cần Thơ', 'FPTU Cần Thơ', 
 ARRAY['FPT Cần Thơ', 'FPTU CT', 'FPT CT', 'Đại học FPT Cần Thơ', 'FPT Miền Tây'], 
 'Cần Thơ', ARRAY['CT', 'Can Tho', 'Cần Thơ', 'Miền Tây', 'ĐBSCL'],
 '600 Nguyễn Văn Cừ, An Bình, Ninh Kiều, Cần Thơ', 
 '0292 730 3636', '<EMAIL>', 9170000, -30.0),

('QN', 'FPT University Quy Nhơn', 'FPTU Quy Nhơn', 
 ARRAY['FPT Quy Nhơn', 'FPTU QN', 'FPT QN', 'Đại học FPT Quy Nhơn', 'FPT Bình Định'], 
 'Quy Nhơn', ARRAY['QN', 'Quy Nhon', 'Bình Định', 'Miền Trung'],
 'Khu đô thị FPT City Quy Nhon, P. Ghềnh Ráng, TP. Quy Nhon, Bình Định', 
 '0256 7300 999', '<EMAIL>', 6550000, -50.0);

-- =====================================================
-- INSERT DATA - ALL OFFICIAL PROGRAMS WITH ALIASES
-- =====================================================
INSERT INTO programs (code, name, name_en, name_short, aliases, keywords, department) VALUES
-- CÔNG NGHỆ THÔNG TIN
('SE', 'Kỹ thuật phần mềm', 'Software Engineering', 'KTPM', 
 ARRAY['Software Engineering', 'KTPM', 'Phần mềm', 'Software Dev', 'Lập trình', 'Dev'], 
 ARRAY['programming', 'coding', 'software', 'app', 'web', 'mobile'], 
 'Công nghệ thông tin'),

('IS', 'Hệ thống thông tin', 'Information Systems', 'HTTT', 
 ARRAY['Information Systems', 'HTTT', 'Hệ thống', 'IT Systems', 'Thông tin học'], 
 ARRAY['database', 'system', 'analysis', 'erp', 'crm'], 
 'Công nghệ thông tin'),

('AI', 'Trí tuệ nhân tạo', 'Artificial Intelligence', 'TTNT', 
 ARRAY['Artificial Intelligence', 'TTNT', 'Machine Learning', 'ML', 'AI', 'Deep Learning'], 
 ARRAY['ai', 'ml', 'neural', 'robot', 'chatbot', 'automation'], 
 'Công nghệ thông tin'),

('IA', 'An toàn thông tin', 'Information Assurance', 'ATTT', 
 ARRAY['Information Assurance', 'ATTT', 'Cyber Security', 'Security', 'Bảo mật'], 
 ARRAY['security', 'cyber', 'hack', 'protection', 'firewall'], 
 'Công nghệ thông tin'),

('IOT', 'Công nghệ ô tô số', 'Internet of Things', 'CNOTS', 
 ARRAY['Internet of Things', 'CNOTS', 'IoT', 'Smart Car', 'Ô tô thông minh'], 
 ARRAY['iot', 'smart', 'car', 'automotive', 'sensor'], 
 'Công nghệ thông tin'),

('DS', 'Thiết kế vi mạch bán dẫn', 'Digital IC Design', 'TKVM', 
 ARRAY['Digital IC Design', 'TKVM', 'IC Design', 'Chip Design', 'Vi mạch'], 
 ARRAY['chip', 'semiconductor', 'ic', 'circuit', 'electronics'], 
 'Công nghệ thông tin'),

('DGD', 'Thiết kế mỹ thuật số', 'Digital Graphic Design', 'TKMT', 
 ARRAY['Digital Graphic Design', 'TKMT', 'Graphic Design', 'UI/UX', 'Thiết kế'], 
 ARRAY['design', 'ui', 'ux', 'graphic', 'photoshop', 'creative'], 
 'Công nghệ thông tin'),

-- QUẢN TRỊ KINH DOANH  
('MKT', 'Digital Marketing', 'Digital Marketing', 'Marketing', 
 ARRAY['Digital Marketing', 'Marketing', 'MKT', 'Tiếp thị', 'Tiếp thị số'], 
 ARRAY['marketing', 'seo', 'social', 'ads', 'brand', 'digital'], 
 'Quản trị kinh doanh'),

('IB', 'Kinh doanh quốc tế', 'International Business', 'KDQT', 
 ARRAY['International Business', 'KDQT', 'Kinh tế quốc tế', 'Global Business'], 
 ARRAY['international', 'global', 'export', 'import', 'trade'], 
 'Quản trị kinh doanh'),

('HM', 'Quản trị khách sạn', 'Hotel Management', 'QTKS', 
 ARRAY['Hotel Management', 'QTKS', 'Hospitality', 'Khách sạn', 'Du lịch khách sạn'], 
 ARRAY['hotel', 'hospitality', 'tourism', 'service', 'resort'], 
 'Quản trị kinh doanh'),

('TM', 'Quản trị dịch vụ du lịch và lữ hành', 'Tourism Management', 'QTDL', 
 ARRAY['Tourism Management', 'QTDL', 'Du lịch', 'Travel', 'Lữ hành'], 
 ARRAY['tourism', 'travel', 'tour', 'destination', 'hospitality'], 
 'Quản trị kinh doanh'),

('CF', 'Tài chính doanh nghiệp', 'Corporate Finance', 'TCDN', 
 ARRAY['Corporate Finance', 'TCDN', 'Tài chính', 'Finance', 'Kế toán tài chính'], 
 ARRAY['finance', 'accounting', 'corporate', 'investment', 'money'], 
 'Quản trị kinh doanh'),

('BF', 'Ngân hàng số - Tài chính', 'Banking & Finance', 'NHSTC', 
 ARRAY['Banking & Finance', 'NHSTC', 'Ngân hàng', 'Bank', 'Digital Banking'], 
 ARRAY['banking', 'finance', 'digital', 'fintech', 'payment'], 
 'Quản trị kinh doanh'),

('FT', 'Công nghệ tài chính', 'FinTech', 'CNTP', 
 ARRAY['FinTech', 'CNTP', 'Fintech', 'Financial Technology', 'Tài chính công nghệ'], 
 ARRAY['fintech', 'blockchain', 'payment', 'digital', 'crypto'], 
 'Quản trị kinh doanh'),

('IF', 'Tài chính đầu tư', 'Investment Finance', 'TCDT', 
 ARRAY['Investment Finance', 'TCDT', 'Đầu tư', 'Investment', 'Chứng khoán'], 
 ARRAY['investment', 'stock', 'portfolio', 'trading', 'securities'], 
 'Quản trị kinh doanh'),

('SCM', 'Logistics & quản lý chuỗi cung ứng toàn cầu', 'Supply Chain Management', 'SCM', 
 ARRAY['Supply Chain Management', 'SCM', 'Logistics', 'Chuỗi cung ứng', 'Vận tải'], 
 ARRAY['logistics', 'supply', 'chain', 'shipping', 'warehouse'], 
 'Quản trị kinh doanh'),

-- CÔNG NGHỆ TRUYỀN THÔNG
('MC', 'Truyền thông đa phương tiện', 'Multimedia Communications', 'TTĐPT', 
 ARRAY['Multimedia Communications', 'TTĐPT', 'Truyền thông', 'Media', 'Đa phương tiện'], 
 ARRAY['media', 'multimedia', 'communication', 'journalism', 'content'], 
 'Công nghệ truyền thông'),

('PR', 'Quan hệ công chúng', 'Public Relations', 'QHCC', 
 ARRAY['Public Relations', 'QHCC', 'PR', 'Quan hệ công chúng', 'Truyền thông PR'], 
 ARRAY['pr', 'public', 'relations', 'communication', 'media'], 
 'Công nghệ truyền thông'),

-- LUẬT
('BL', 'Luật kinh tế', 'Business Law', 'LKT', 
 ARRAY['Business Law', 'LKT', 'Luật', 'Law', 'Luật thương mại'], 
 ARRAY['law', 'legal', 'business', 'commercial', 'contract'], 
 'Luật'),

('IL', 'Luật thương mại quốc tế', 'International Law', 'LQTQT', 
 ARRAY['International Law', 'LQTQT', 'Luật quốc tế', 'Global Law'], 
 ARRAY['law', 'international', 'trade', 'global', 'treaty'], 
 'Luật'),

-- NGÔN NGỮ
('EL', 'Ngôn ngữ Anh', 'English Language', 'NNA', 
 ARRAY['English Language', 'NNA', 'Tiếng Anh', 'English', 'Anh ngữ'], 
 ARRAY['english', 'language', 'linguistics', 'teaching', 'translation'], 
 'Ngôn ngữ Anh'),

('CE', 'Song ngữ Trung -- Anh', 'Chinese-English', 'SN Trung-Anh', 
 ARRAY['Chinese-English', 'SN Trung-Anh', 'Trung - Anh', 'Tiếng Trung'], 
 ARRAY['chinese', 'english', 'mandarin', 'translation', 'bilingual'], 
 'Ngôn ngữ Trung Quốc'),

('JE', 'Song ngữ Nhật -- Anh', 'Japanese-English', 'SN Nhật-Anh', 
 ARRAY['Japanese-English', 'SN Nhật-Anh', 'Nhật - Anh', 'Tiếng Nhật'], 
 ARRAY['japanese', 'english', 'nihongo', 'translation', 'bilingual'], 
 'Ngôn ngữ Nhật'),

('KE', 'Song ngữ Hàn -- Anh', 'Korean-English', 'SN Hàn-Anh', 
 ARRAY['Korean-English', 'SN Hàn-Anh', 'Hàn - Anh', 'Tiếng Hàn'], 
 ARRAY['korean', 'english', 'hangul', 'translation', 'bilingual'], 
 'Ngôn ngữ Hàn Quốc');

-- =====================================================
-- INSERT ADDITIONAL PROGRAM ALIASES
-- =====================================================
INSERT INTO program_aliases (program_id, alias_name, alias_type) VALUES
-- SE aliases
((SELECT id FROM programs WHERE code = 'SE'), 'Lập trình viên', 'industry'),
((SELECT id FROM programs WHERE code = 'SE'), 'Developer', 'industry'),
((SELECT id FROM programs WHERE code = 'SE'), 'Coder', 'informal'),
((SELECT id FROM programs WHERE code = 'SE'), 'Software Dev', 'abbreviation'),

-- AI aliases  
((SELECT id FROM programs WHERE code = 'AI'), 'Machine Learning', 'industry'),
((SELECT id FROM programs WHERE code = 'AI'), 'Deep Learning', 'industry'),
((SELECT id FROM programs WHERE code = 'AI'), 'Data Science', 'related'),
((SELECT id FROM programs WHERE code = 'AI'), 'Robot', 'informal'),

-- MKT aliases
((SELECT id FROM programs WHERE code = 'MKT'), 'Social Media Marketing', 'industry'),
((SELECT id FROM programs WHERE code = 'MKT'), 'SEO', 'industry'),
((SELECT id FROM programs WHERE code = 'MKT'), 'Content Marketing', 'industry'),
((SELECT id FROM programs WHERE code = 'MKT'), 'Influencer', 'informal'),

-- Add more aliases for other programs as needed
((SELECT id FROM programs WHERE code = 'FT'), 'Blockchain', 'industry'),
((SELECT id FROM programs WHERE code = 'FT'), 'Cryptocurrency', 'industry'),
((SELECT id FROM programs WHERE code = 'DGD'), 'UI Designer', 'industry'),
((SELECT id FROM programs WHERE code = 'DGD'), 'UX Designer', 'industry');

-- =====================================================
-- INSERT ADDITIONAL CAMPUS ALIASES
-- =====================================================
INSERT INTO campus_aliases (campus_id, alias_name, alias_type) VALUES
-- Hanoi aliases
((SELECT id FROM campuses WHERE code = 'HN'), 'Miền Bắc', 'region'),
((SELECT id FROM campuses WHERE code = 'HN'), 'Thủ đô', 'city'),
((SELECT id FROM campuses WHERE code = 'HN'), 'Hòa Lạc', 'location'),

-- HCM aliases
((SELECT id FROM campuses WHERE code = 'HCM'), 'Miền Nam', 'region'),
((SELECT id FROM campuses WHERE code = 'HCM'), 'Sài Gòn', 'city'),
((SELECT id FROM campuses WHERE code = 'HCM'), 'Thủ Đức', 'location'),

-- Da Nang aliases
((SELECT id FROM campuses WHERE code = 'DN'), 'Miền Trung', 'region'),
((SELECT id FROM campuses WHERE code = 'DN'), 'Đà Nẵng', 'city'),

-- Can Tho aliases
((SELECT id FROM campuses WHERE code = 'CT'), 'Miền Tây', 'region'),
((SELECT id FROM campuses WHERE code = 'CT'), 'ĐBSCL', 'region'),
((SELECT id FROM campuses WHERE code = 'CT'), 'Delta', 'region'),

-- Quy Nhon aliases
((SELECT id FROM campuses WHERE code = 'QN'), 'Bình Định', 'city'),
((SELECT id FROM campuses WHERE code = 'QN'), 'Miền Trung', 'region'),
((SELECT id FROM campuses WHERE code = 'QN'), 'Quy Nhơn', 'city');

-- =====================================================
-- INSERT DATA - OFFICIAL PROGRESSIVE TUITION (HN & HCM Base)
-- =====================================================

-- IT Programs (HN & HCM - Base pricing)
INSERT INTO progressive_tuition (program_id, year, tier_1_fee, tier_2_fee, tier_3_fee, campus_code) VALUES
((SELECT id FROM programs WHERE code = 'SE'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'SE'), 2025, 31600000, 33600000, 35800000, 'HCM'),
((SELECT id FROM programs WHERE code = 'IS'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'IS'), 2025, 31600000, 33600000, 35800000, 'HCM'),
((SELECT id FROM programs WHERE code = 'AI'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'AI'), 2025, 31600000, 33600000, 35800000, 'HCM'),
((SELECT id FROM programs WHERE code = 'IA'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'IA'), 2025, 31600000, 33600000, 35800000, 'HCM'),
((SELECT id FROM programs WHERE code = 'IOT'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'IOT'), 2025, 31600000, 33600000, 35800000, 'HCM'),
((SELECT id FROM programs WHERE code = 'DS'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'DS'), 2025, 31600000, 33600000, 35800000, 'HCM'),
((SELECT id FROM programs WHERE code = 'DGD'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'DGD'), 2025, 31600000, 33600000, 35800000, 'HCM');

-- IT Programs (DN, CT, QN - With discount applied to base price)
INSERT INTO progressive_tuition (program_id, year, tier_1_fee, tier_2_fee, tier_3_fee, campus_code) VALUES
-- DN Campus (-30%)
((SELECT id FROM programs WHERE code = 'SE'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'IS'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'AI'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'IA'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'IOT'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'DS'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'DGD'), 2025, 22120000, 23520000, 25060000, 'DN'),

-- CT Campus (-30%)
((SELECT id FROM programs WHERE code = 'SE'), 2025, 22120000, 23520000, 25060000, 'CT'),
((SELECT id FROM programs WHERE code = 'IS'), 2025, 22120000, 23520000, 25060000, 'CT'),
((SELECT id FROM programs WHERE code = 'AI'), 2025, 22120000, 23520000, 25060000, 'CT'),
((SELECT id FROM programs WHERE code = 'IA'), 2025, 22120000, 23520000, 25060000, 'CT'),
((SELECT id FROM programs WHERE code = 'IOT'), 2025, 22120000, 23520000, 25060000, 'CT'),
((SELECT id FROM programs WHERE code = 'DS'), 2025, 22120000, 23520000, 25060000, 'CT'),
((SELECT id FROM programs WHERE code = 'DGD'), 2025, 22120000, 23520000, 25060000, 'CT'),

-- QN Campus (-50%)
((SELECT id FROM programs WHERE code = 'SE'), 2025, 15800000, 16800000, 17900000, 'QN'),
((SELECT id FROM programs WHERE code = 'IS'), 2025, 15800000, 16800000, 17900000, 'QN'),
((SELECT id FROM programs WHERE code = 'AI'), 2025, 15800000, 16800000, 17900000, 'QN'),
((SELECT id FROM programs WHERE code = 'IA'), 2025, 15800000, 16800000, 17900000, 'QN'),
((SELECT id FROM programs WHERE code = 'IOT'), 2025, 15800000, 16800000, 17900000, 'QN'),
((SELECT id FROM programs WHERE code = 'DS'), 2025, 15800000, 16800000, 17900000, 'QN'),
((SELECT id FROM programs WHERE code = 'DGD'), 2025, 15800000, 16800000, 17900000, 'QN');

-- Business Programs (HN & HCM - Base pricing)
INSERT INTO progressive_tuition (program_id, year, tier_1_fee, tier_2_fee, tier_3_fee, campus_code) VALUES
((SELECT id FROM programs WHERE code = 'MKT'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'MKT'), 2025, 31600000, 33600000, 35800000, 'HCM'),
((SELECT id FROM programs WHERE code = 'IB'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'IB'), 2025, 31600000, 33600000, 35800000, 'HCM'),
((SELECT id FROM programs WHERE code = 'CF'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'CF'), 2025, 31600000, 33600000, 35800000, 'HCM'),
((SELECT id FROM programs WHERE code = 'BF'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'BF'), 2025, 31600000, 33600000, 35800000, 'HCM'),
((SELECT id FROM programs WHERE code = 'FT'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'FT'), 2025, 31600000, 33600000, 35800000, 'HCM'),
((SELECT id FROM programs WHERE code = 'IF'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'IF'), 2025, 31600000, 33600000, 35800000, 'HCM'),
((SELECT id FROM programs WHERE code = 'SCM'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'SCM'), 2025, 31600000, 33600000, 35800000, 'HCM');

-- Business Programs (DN, CT, QN - With discount)
INSERT INTO progressive_tuition (program_id, year, tier_1_fee, tier_2_fee, tier_3_fee, campus_code) VALUES
-- DN Campus (-30%)
((SELECT id FROM programs WHERE code = 'MKT'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'IB'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'CF'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'BF'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'FT'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'IF'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'SCM'), 2025, 22120000, 23520000, 25060000, 'DN'),

-- CT Campus (-30%)
((SELECT id FROM programs WHERE code = 'MKT'), 2025, 22120000, 23520000, 25060000, 'CT'),
((SELECT id FROM programs WHERE code = 'IB'), 2025, 22120000, 23520000, 25060000, 'CT'),
((SELECT id FROM programs WHERE code = 'CF'), 2025, 22120000, 23520000, 25060000, 'CT'),
((SELECT id FROM programs WHERE code = 'BF'), 2025, 22120000, 23520000, 25060000, 'CT'),
((SELECT id FROM programs WHERE code = 'FT'), 2025, 22120000, 23520000, 25060000, 'CT'),
((SELECT id FROM programs WHERE code = 'IF'), 2025, 22120000, 23520000, 25060000, 'CT'),
((SELECT id FROM programs WHERE code = 'SCM'), 2025, 22120000, 23520000, 25060000, 'CT'),

-- QN Campus (-50%)
((SELECT id FROM programs WHERE code = 'MKT'), 2025, 15800000, 16800000, 17900000, 'QN'),
((SELECT id FROM programs WHERE code = 'IB'), 2025, 15800000, 16800000, 17900000, 'QN'),
((SELECT id FROM programs WHERE code = 'CF'), 2025, 15800000, 16800000, 17900000, 'QN'),
((SELECT id FROM programs WHERE code = 'BF'), 2025, 15800000, 16800000, 17900000, 'QN'),
((SELECT id FROM programs WHERE code = 'FT'), 2025, 15800000, 16800000, 17900000, 'QN'),
((SELECT id FROM programs WHERE code = 'IF'), 2025, 15800000, 16800000, 17900000, 'QN'),
((SELECT id FROM programs WHERE code = 'SCM'), 2025, 15800000, 16800000, 17900000, 'QN');

-- Tourism Programs (DN, CT only - special pricing)
INSERT INTO progressive_tuition (program_id, year, tier_1_fee, tier_2_fee, tier_3_fee, campus_code) VALUES
((SELECT id FROM programs WHERE code = 'HM'), 2025, 15480000, 16460000, 17540000, 'DN'),
((SELECT id FROM programs WHERE code = 'HM'), 2025, 15480000, 16460000, 17540000, 'CT'),
((SELECT id FROM programs WHERE code = 'TM'), 2025, 15480000, 16460000, 17540000, 'DN'),
((SELECT id FROM programs WHERE code = 'TM'), 2025, 15480000, 16460000, 17540000, 'CT');

-- Communications Programs (All campuses)
INSERT INTO progressive_tuition (program_id, year, tier_1_fee, tier_2_fee, tier_3_fee, campus_code) VALUES
-- HN & HCM (Base pricing)
((SELECT id FROM programs WHERE code = 'MC'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'MC'), 2025, 31600000, 33600000, 35800000, 'HCM'),
((SELECT id FROM programs WHERE code = 'PR'), 2025, 31600000, 33600000, 35800000, 'HN'),
((SELECT id FROM programs WHERE code = 'PR'), 2025, 31600000, 33600000, 35800000, 'HCM'),

-- DN & CT (-30%)
((SELECT id FROM programs WHERE code = 'MC'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'MC'), 2025, 22120000, 23520000, 25060000, 'CT'),
((SELECT id FROM programs WHERE code = 'PR'), 2025, 22120000, 23520000, 25060000, 'DN'),
((SELECT id FROM programs WHERE code = 'PR'), 2025, 22120000, 23520000, 25060000, 'CT'),

-- QN (-50%)
((SELECT id FROM programs WHERE code = 'MC'), 2025, 15800000, 16800000, 17900000, 'QN'),
((SELECT id FROM programs WHERE code = 'PR'), 2025, 15800000, 16800000, 17900000, 'QN');

-- Law Programs (Lower tier pricing - All campuses)
INSERT INTO progressive_tuition (program_id, year, tier_1_fee, tier_2_fee, tier_3_fee, campus_code) VALUES
-- HN & HCM (Base law pricing)
((SELECT id FROM programs WHERE code = 'BL'), 2025, 22120000, 23520000, 25060000, 'HN'),
((SELECT id FROM programs WHERE code = 'BL'), 2025, 22120000, 23520000, 25060000, 'HCM'),
((SELECT id FROM programs WHERE code = 'IL'), 2025, 22120000, 23520000, 25060000, 'HN'),
((SELECT id FROM programs WHERE code = 'IL'), 2025, 22120000, 23520000, 25060000, 'HCM'),

-- DN & CT (-30% from law base)
((SELECT id FROM programs WHERE code = 'BL'), 2025, 15484000, 16464000, 17542000, 'DN'),
((SELECT id FROM programs WHERE code = 'BL'), 2025, 15484000, 16464000, 17542000, 'CT'),
((SELECT id FROM programs WHERE code = 'IL'), 2025, 15484000, 16464000, 17542000, 'DN'),
((SELECT id FROM programs WHERE code = 'IL'), 2025, 15484000, 16464000, 17542000, 'CT'),

-- QN (-50% from law base)
((SELECT id FROM programs WHERE code = 'BL'), 2025, 11060000, 11760000, 12530000, 'QN'),
((SELECT id FROM programs WHERE code = 'IL'), 2025, 11060000, 11760000, 12530000, 'QN');

-- Language Programs (Lower tier pricing - All campuses)
INSERT INTO progressive_tuition (program_id, year, tier_1_fee, tier_2_fee, tier_3_fee, campus_code) VALUES
-- HN & HCM (Base language pricing)
((SELECT id FROM programs WHERE code = 'EL'), 2025, 22120000, 23520000, 25060000, 'HN'),
((SELECT id FROM programs WHERE code = 'EL'), 2025, 22120000, 23520000, 25060000, 'HCM'),
((SELECT id FROM programs WHERE code = 'CE'), 2025, 22120000, 23520000, 25060000, 'HN'),
((SELECT id FROM programs WHERE code = 'CE'), 2025, 22120000, 23520000, 25060000, 'HCM'),
((SELECT id FROM programs WHERE code = 'JE'), 2025, 22120000, 23520000, 25060000, 'HN'),
((SELECT id FROM programs WHERE code = 'JE'), 2025, 22120000, 23520000, 25060000, 'HCM'),
((SELECT id FROM programs WHERE code = 'KE'), 2025, 22120000, 23520000, 25060000, 'HN'),
((SELECT id FROM programs WHERE code = 'KE'), 2025, 22120000, 23520000, 25060000, 'HCM'),

-- DN & CT (-30% from language base)
((SELECT id FROM programs WHERE code = 'EL'), 2025, 15484000, 16464000, 17542000, 'DN'),
((SELECT id FROM programs WHERE code = 'EL'), 2025, 15484000, 16464000, 17542000, 'CT'),
((SELECT id FROM programs WHERE code = 'CE'), 2025, 15484000, 16464000, 17542000, 'DN'),
((SELECT id FROM programs WHERE code = 'CE'), 2025, 15484000, 16464000, 17542000, 'CT'),
((SELECT id FROM programs WHERE code = 'JE'), 2025, 15484000, 16464000, 17542000, 'DN'),
((SELECT id FROM programs WHERE code = 'JE'), 2025, 15484000, 16464000, 17542000, 'CT'),
((SELECT id FROM programs WHERE code = 'KE'), 2025, 15484000, 16464000, 17542000, 'DN'),
((SELECT id FROM programs WHERE code = 'KE'), 2025, 15484000, 16464000, 17542000, 'CT'),

-- QN (-50% from language base)
((SELECT id FROM programs WHERE code = 'EL'), 2025, 11060000, 11760000, 12530000, 'QN'),
((SELECT id FROM programs WHERE code = 'CE'), 2025, 11060000, 11760000, 12530000, 'QN'),
((SELECT id FROM programs WHERE code = 'JE'), 2025, 11060000, 11760000, 12530000, 'QN'),
((SELECT id FROM programs WHERE code = 'KE'), 2025, 11060000, 11760000, 12530000, 'QN');

-- =====================================================
-- INSERT DATA - OFFICIAL 2025 SCHOLARSHIPS WITH ALIASES
-- =====================================================
INSERT INTO scholarships (code, name, name_aliases, type, recipients, percentage, requirements, year) VALUES
('GLOBAL_EXPERT', 'Học bổng Chuyên gia Toàn cầu', 
 ARRAY['Chuyên gia Toàn cầu', 'Global Expert', 'HSG Quốc gia IT', 'Học bổng 100%'], 
 'global_expert', 100, 100, 'Đạt giải Nhất/Nhì/Ba trong kỳ thi chọn học sinh giỏi cấp quốc gia, đăng ký học ngành Công nghệ thông tin', 2025),

('SCHOOL_PATH', 'Học bổng Học đường', 
 ARRAY['Học đường', 'School Path', 'SchoolRank Top10', 'HSG Trường'], 
 'school_path', 900, 100, 'Học sinh có SchoolRank thuộc Top10, mỗi trường THPT Khu vực 1 một suất', 2025),

('FULL_TUITION', 'Học bổng Toàn phần', 
 ARRAY['Toàn phần', 'Full Scholarship', '100%', 'Full Tuition'], 
 'full', 300, 100, 'Đội tuyển Olympic quốc tế hoặc Giải Nhất HSG quốc gia hoặc >=90% ĐGNL hoặc >=9.0 THPT', 2025),

('PARTIAL_2YEAR', 'Học bổng 2 năm', 
 ARRAY['2 năm', '2-year', '50% 2 năm', 'Partial 2Y'], 
 'partial_2year', 500, 50, 'Giải Nhì HSG quốc gia hoặc >=85% ĐGNL hoặc >=8.5 THPT', 2025),

('PARTIAL_1YEAR', 'Học bổng 1 năm', 
 ARRAY['1 năm', '1-year', '25% 1 năm', 'Partial 1Y'], 
 'partial_1year', 1000, 25, 'Giải Ba HSG quốc gia hoặc >=80% ĐGNL hoặc >=8.0 THPT', 2025);

-- =====================================================
-- INSERT DATA - ADMISSION METHODS 2025
-- =====================================================
INSERT INTO admission_methods (method_code, name, name_aliases, requirements, threshold_description, year) VALUES
('M1', 'Xét kết quả học tập cấp THPT (học bạ)', 
 ARRAY['Học bạ', 'School Record', 'Điểm THPT', 'M1'], 
 'Top 50 SchoolRank + Điểm kỳ 2 lớp 12 (Toán + 2 môn) >= 21', 
 'Top 50 SchoolRank học bạ', 2025),

('M2', 'Kết quả đánh giá năng lực ĐH Quốc gia', 
 ARRAY['ĐGNL', 'Đánh giá năng lực', 'National University Test', 'M2'], 
 'ĐHQG HN >= 80 điểm, ĐHQG HCM >= 609 điểm', 
 'Top 50 kỳ thi ĐGNL', 2025),

('M3', 'Kết quả thi tốt nghiệp THPT', 
 ARRAY['Tốt nghiệp THPT', 'THPT', 'High School Graduation', 'M3'], 
 'Toán + 2 môn bất kỳ + Điểm ưu tiên', 
 'Top 50 thi THPT', 2025),

('M4', 'Phương thức tuyển sinh khác', 
 ARRAY['Tuyển sinh khác', 'Other Methods', 'Văn bằng khác', 'M4'], 
 'BTEC HND, Melbourne Polytechnic, FUNiX, Cao đẳng FPT, THPT FPT, THPT nước ngoài, etc.', 
 'Văn bằng/Chứng chỉ đạt chuẩn', 2025);

-- =====================================================
-- INSERT DATA - PROGRAM CAMPUS AVAILABILITY
-- =====================================================

-- All programs available at HN & HCM
INSERT INTO program_campus_availability (program_id, campus_id, is_available, year)
SELECT p.id, c.id, true, 2025
FROM programs p
CROSS JOIN campuses c 
WHERE c.code IN ('HN', 'HCM') AND p.is_active = true;

-- DN Campus - Exclude IS (Hệ thống thông tin)
INSERT INTO program_campus_availability (program_id, campus_id, is_available, year)
SELECT p.id, c.id, true, 2025
FROM programs p
CROSS JOIN campuses c 
WHERE c.code = 'DN' AND p.code != 'IS' AND p.is_active = true;

-- CT Campus - Exclude IS (Hệ thống thông tin)  
INSERT INTO program_campus_availability (program_id, campus_id, is_available, year)
SELECT p.id, c.id, true, 2025
FROM programs p
CROSS JOIN campuses c 
WHERE c.code = 'CT' AND p.code != 'IS' AND p.is_active = true;

-- QN Campus - Limited programs only
INSERT INTO program_campus_availability (program_id, campus_id, is_available, year)
SELECT p.id, c.id, true, 2025
FROM programs p
CROSS JOIN campuses c 
WHERE c.code = 'QN' 
AND p.code IN ('SE', 'AI', 'IA', 'IOT', 'DS', 'DGD', 'MKT', 'IB', 'FT', 'SCM', 'MC', 'PR', 'BL', 'EL')
AND p.is_active = true;

-- =====================================================
-- INSERT DATA - SAMPLE ADMISSION QUOTAS (Based on 13,127 total)
-- =====================================================
INSERT INTO admission_quotas (program_id, campus_id, total_quota, year)
VALUES
-- IT Programs quotas (8,125 total)
((SELECT id FROM programs WHERE code = 'SE'), (SELECT id FROM campuses WHERE code = 'HN'), 1500, 2025),
((SELECT id FROM programs WHERE code = 'SE'), (SELECT id FROM campuses WHERE code = 'HCM'), 1500, 2025),
((SELECT id FROM programs WHERE code = 'AI'), (SELECT id FROM campuses WHERE code = 'HN'), 800, 2025),
((SELECT id FROM programs WHERE code = 'AI'), (SELECT id FROM campuses WHERE code = 'HCM'), 800, 2025),
-- Add more quotas as needed...

-- Business Programs quotas (3,164 total)
((SELECT id FROM programs WHERE code = 'MKT'), (SELECT id FROM campuses WHERE code = 'HN'), 400, 2025),
((SELECT id FROM programs WHERE code = 'MKT'), (SELECT id FROM campuses WHERE code = 'HCM'), 400, 2025),
((SELECT id FROM programs WHERE code = 'IB'), (SELECT id FROM campuses WHERE code = 'HN'), 300, 2025),
((SELECT id FROM programs WHERE code = 'IB'), (SELECT id FROM campuses WHERE code = 'HCM'), 300, 2025);
-- Add more quotas as needed...

-- =====================================================
-- UPDATE CAMPUS ADDITIONAL INFORMATION
-- =====================================================
UPDATE campuses SET 
    virtual_tour_url = 'https://viewdaihoc.fpt.edu.vn/fpt-ha-noi/index.html',
    description = 'Campus xanh rộng 30ha tại Khu Công nghệ cao Hòa Lạc với kiến trúc hình rồng độc đáo',
    facilities = ARRAY['Library', 'Labs', 'Sports Center', 'Cafeteria', 'Dormitory', 'Innovation Hub'],
    accommodations_capacity = 5000
WHERE code = 'HN';

UPDATE campuses SET 
    virtual_tour_url = 'https://viewdaihoc.fpt.edu.vn/fpt-ho-chi-minh/',
    description = 'Campus hiện đại tại Khu Công nghệ cao TP.HCM với kiến trúc núi xanh độc đáo',
    facilities = ARRAY['Library', 'Labs', 'Innovation Center', 'Cafeteria', 'Student Center'],
    accommodations_capacity = 3000
WHERE code = 'HCM';

UPDATE campuses SET 
    virtual_tour_url = 'https://university.fpt.edu.vn/dn/tham-quan-truc-tuyen/',
    description = 'Campus FPT City Đà Nẵng - thành phố đáng sống nhất Việt Nam',
    facilities = ARRAY['Library', 'Labs', 'Sports Facilities', 'Cafeteria', 'Dormitory'],
    accommodations_capacity = 2000
WHERE code = 'DN';

UPDATE campuses SET 
    virtual_tour_url = 'https://viewdaihoc.fpt.edu.vn/fpt-can-tho/index.html',
    description = 'Campus "ngôi trường mọc lên ở trời Tây" với kiến trúc châu Âu đương đại',
    facilities = ARRAY['Library', 'Labs', 'Sports Center', 'Cafeteria', 'Student Housing'],
    accommodations_capacity = 1500
WHERE code = 'CT';

UPDATE campuses SET 
    virtual_tour_url = 'https://viewdaihoc.fpt.edu.vn/fpt-quy-nhon/index.html',
    description = 'Campus AI Quy Nhơn - "thung lũng Silicon" tương lai của Việt Nam',
    facilities = ARRAY['AI Labs', 'Library', 'Innovation Center', 'Cafeteria', 'Tech Hub'],
    accommodations_capacity = 1000
WHERE code = 'QN';

-- =====================================================
-- CREATE ADDITIONAL INDEXES
-- =====================================================
CREATE INDEX idx_admission_methods_code ON admission_methods(method_code);
CREATE INDEX idx_admission_quotas_program_campus ON admission_quotas(program_id, campus_id);
CREATE INDEX idx_program_campus_availability_lookup ON program_campus_availability(program_id, campus_id, year);

-- =====================================================
-- ENHANCED FUNCTIONS: Search with Alias Support
-- =====================================================

-- Function: Search programs by any name or alias
CREATE OR REPLACE FUNCTION search_programs_by_name(
    search_text VARCHAR
) RETURNS TABLE (
    code VARCHAR,
    name VARCHAR,
    name_en VARCHAR,
    name_short VARCHAR,
    department VARCHAR,
    match_type VARCHAR,
    matched_term VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    -- Direct name matches
    SELECT 
        p.code, p.name, p.name_en, p.name_short, p.department,
        'direct'::VARCHAR as match_type,
        p.name::VARCHAR as matched_term
    FROM programs p
    WHERE p.is_active = true 
    AND (
        LOWER(p.name) LIKE LOWER('%' || search_text || '%') OR
        LOWER(p.name_en) LIKE LOWER('%' || search_text || '%') OR
        LOWER(p.name_short) LIKE LOWER('%' || search_text || '%') OR
        LOWER(p.code) = LOWER(search_text)
    )
    
    UNION ALL
    
    -- Alias matches from array
    SELECT 
        p.code, p.name, p.name_en, p.name_short, p.department,
        'alias_array'::VARCHAR as match_type,
        unnest(p.aliases)::VARCHAR as matched_term
    FROM programs p
    WHERE p.is_active = true 
    AND EXISTS (
        SELECT 1 FROM unnest(p.aliases) as alias
        WHERE LOWER(alias) LIKE LOWER('%' || search_text || '%')
    )
    
    UNION ALL
    
    -- Keyword matches
    SELECT 
        p.code, p.name, p.name_en, p.name_short, p.department,
        'keyword'::VARCHAR as match_type,
        unnest(p.keywords)::VARCHAR as matched_term
    FROM programs p
    WHERE p.is_active = true 
    AND EXISTS (
        SELECT 1 FROM unnest(p.keywords) as keyword
        WHERE LOWER(keyword) LIKE LOWER('%' || search_text || '%')
    )
    
    UNION ALL
    
    -- External alias table matches
    SELECT 
        p.code, p.name, p.name_en, p.name_short, p.department,
        pa.alias_type::VARCHAR as match_type,
        pa.alias_name::VARCHAR as matched_term
    FROM programs p
    JOIN program_aliases pa ON pa.program_id = p.id
    WHERE p.is_active = true 
    AND LOWER(pa.alias_name) LIKE LOWER('%' || search_text || '%');
END;
$$ LANGUAGE plpgsql;

-- Function: Search campuses by any name or alias
CREATE OR REPLACE FUNCTION search_campuses_by_name(
    search_text VARCHAR
) RETURNS TABLE (
    code VARCHAR,
    name VARCHAR,
    name_short VARCHAR,
    city VARCHAR,
    match_type VARCHAR,
    matched_term VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    -- Direct name matches
    SELECT 
        c.code, c.name, c.name_short, c.city,
        'direct'::VARCHAR as match_type,
        c.name::VARCHAR as matched_term
    FROM campuses c
    WHERE c.is_active = true 
    AND (
        LOWER(c.name) LIKE LOWER('%' || search_text || '%') OR
        LOWER(c.name_short) LIKE LOWER('%' || search_text || '%') OR
        LOWER(c.city) LIKE LOWER('%' || search_text || '%') OR
        LOWER(c.code) = LOWER(search_text)
    )
    
    UNION ALL
    
    -- Campus alias matches from array
    SELECT 
        c.code, c.name, c.name_short, c.city,
        'alias_array'::VARCHAR as match_type,
        unnest(c.aliases)::VARCHAR as matched_term
    FROM campuses c
    WHERE c.is_active = true 
    AND EXISTS (
        SELECT 1 FROM unnest(c.aliases) as alias
        WHERE LOWER(alias) LIKE LOWER('%' || search_text || '%')
    )
    
    UNION ALL
    
    -- City alias matches
    SELECT 
        c.code, c.name, c.name_short, c.city,
        'city_alias'::VARCHAR as match_type,
        unnest(c.city_aliases)::VARCHAR as matched_term
    FROM campuses c
    WHERE c.is_active = true 
    AND EXISTS (
        SELECT 1 FROM unnest(c.city_aliases) as city_alias
        WHERE LOWER(city_alias) LIKE LOWER('%' || search_text || '%')
    )
    
    UNION ALL
    
    -- External alias table matches
    SELECT 
        c.code, c.name, c.name_short, c.city,
        ca.alias_type::VARCHAR as match_type,
        ca.alias_name::VARCHAR as matched_term
    FROM campuses c
    JOIN campus_aliases ca ON ca.campus_id = c.id
    WHERE c.is_active = true 
    AND LOWER(ca.alias_name) LIKE LOWER('%' || search_text || '%');
END;
$$ LANGUAGE plpgsql;

-- Enhanced: Progressive Tuition Calculator with alias search
CREATE OR REPLACE FUNCTION get_tuition_by_alias(
    program_search_text VARCHAR,
    campus_search_text VARCHAR,
    p_year INTEGER DEFAULT NULL
) RETURNS TABLE (
    program_code VARCHAR,
    program_name VARCHAR,
    campus_name VARCHAR,
    foundation_fee DECIMAL,
    tier_1_fee DECIMAL,
    tier_2_fee DECIMAL,
    tier_3_fee DECIMAL,
    total_9_semesters DECIMAL,
    total_with_foundation DECIMAL,
    discount_applied VARCHAR
) AS $$
DECLARE
    target_year INTEGER;
    program_id_found UUID;
    campus_code_found VARCHAR;
BEGIN
    target_year := COALESCE(p_year, EXTRACT(YEAR FROM CURRENT_DATE));
    
    -- Find program by alias
    SELECT p.id INTO program_id_found
    FROM programs p
    WHERE p.is_active = true 
    AND (
        LOWER(p.code) = LOWER(program_search_text) OR
        LOWER(p.name) LIKE LOWER('%' || program_search_text || '%') OR
        LOWER(p.name_short) LIKE LOWER('%' || program_search_text || '%') OR
        EXISTS (SELECT 1 FROM unnest(p.aliases) as alias WHERE LOWER(alias) LIKE LOWER('%' || program_search_text || '%')) OR
        EXISTS (SELECT 1 FROM unnest(p.keywords) as keyword WHERE LOWER(keyword) LIKE LOWER('%' || program_search_text || '%'))
    )
    LIMIT 1;
    
    -- Find campus by alias
    SELECT c.code INTO campus_code_found
    FROM campuses c
    WHERE c.is_active = true 
    AND (
        LOWER(c.code) = LOWER(campus_search_text) OR
        LOWER(c.name) LIKE LOWER('%' || campus_search_text || '%') OR
        LOWER(c.city) LIKE LOWER('%' || campus_search_text || '%') OR
        EXISTS (SELECT 1 FROM unnest(c.aliases) as alias WHERE LOWER(alias) LIKE LOWER('%' || campus_search_text || '%')) OR
        EXISTS (SELECT 1 FROM unnest(c.city_aliases) as city_alias WHERE LOWER(city_alias) LIKE LOWER('%' || campus_search_text || '%'))
    )
    LIMIT 1;
    
    IF program_id_found IS NULL OR campus_code_found IS NULL THEN
        RETURN;
    END IF;
    
    RETURN QUERY
    SELECT 
        p.code::VARCHAR,
        p.name::VARCHAR,
        c.name::VARCHAR,
        c.foundation_fee,
        pt.tier_1_fee,
        pt.tier_2_fee,
        pt.tier_3_fee,
        (pt.tier_1_fee * 3 + pt.tier_2_fee * 3 + pt.tier_3_fee * 3) as total_9_semesters,
        (pt.tier_1_fee * 3 + pt.tier_2_fee * 3 + pt.tier_3_fee * 3 + c.foundation_fee) as total_with_foundation,
        CASE 
            WHEN c.discount_percentage > 0 THEN CONCAT('+', c.discount_percentage::TEXT, '%')
            WHEN c.discount_percentage < 0 THEN CONCAT(c.discount_percentage::TEXT, '%')
            ELSE 'Base pricing'
        END::VARCHAR as discount_applied
    FROM programs p
    JOIN campuses c ON c.code = campus_code_found
    JOIN progressive_tuition pt ON pt.program_id = p.id AND pt.year = target_year AND pt.campus_code = c.code
    WHERE p.id = program_id_found;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE (Enhanced)
-- =====================================================
CREATE INDEX idx_programs_code ON programs(code);
CREATE INDEX idx_programs_department ON programs(department);
CREATE INDEX idx_programs_aliases ON programs USING GIN(aliases);
CREATE INDEX idx_programs_keywords ON programs USING GIN(keywords);

CREATE INDEX idx_campuses_code ON campuses(code);
CREATE INDEX idx_campuses_aliases ON campuses USING GIN(aliases);
CREATE INDEX idx_campuses_city_aliases ON campuses USING GIN(city_aliases);

CREATE INDEX idx_program_aliases_name ON program_aliases(alias_name);
CREATE INDEX idx_program_aliases_type ON program_aliases(alias_type);
CREATE INDEX idx_campus_aliases_name ON campus_aliases(alias_name);
CREATE INDEX idx_campus_aliases_type ON campus_aliases(alias_type);

CREATE INDEX idx_progressive_tuition_program_year_campus ON progressive_tuition(program_id, year, campus_code);
CREATE INDEX idx_scholarships_year ON scholarships(year);
CREATE INDEX idx_scholarships_aliases ON scholarships USING GIN(name_aliases);

-- =====================================================
-- SAMPLE QUERIES FOR TESTING ALIAS FUNCTIONALITY
-- =====================================================

-- Test alias searches:
-- SELECT * FROM search_programs_by_name('AI');
-- SELECT * FROM search_programs_by_name('Machine Learning');
-- SELECT * FROM search_programs_by_name('KTPM');
-- SELECT * FROM search_programs_by_name('lập trình');

-- SELECT * FROM search_campuses_by_name('HN');
-- SELECT * FROM search_campuses_by_name('Sài Gòn');
-- SELECT * FROM search_campuses_by_name('Miền Trung');

-- Test enhanced tuition function:
-- SELECT * FROM get_tuition_by_alias('AI', 'Hà Nội', 2025);
-- SELECT * FROM get_tuition_by_alias('Machine Learning', 'Sài Gòn', 2025);
-- SELECT * FROM get_tuition_by_alias('KTPM', 'Miền Trung', 2025);

-- =====================================================
-- END OF FILE
-- ===================================================== 

-- =====================================================
-- 10. MATERIALIZED VIEWS FOR PERFORMANCE
-- =====================================================

-- Fast program search view with pre-computed data
CREATE MATERIALIZED VIEW mv_program_search AS
SELECT 
    p.id,
    p.code,
    p.name,
    p.name_en,
    p.name_short,
    p.department,
    p.aliases,
    p.keywords,
    array_agg(DISTINCT pa.alias_name) as external_aliases,
    array_agg(DISTINCT c.code) as available_campuses,
    min(pt.tier_1_fee) as min_tuition,
    max(pt.tier_3_fee) as max_tuition,
    p.is_active
FROM programs p
LEFT JOIN program_aliases pa ON pa.program_id = p.id
LEFT JOIN program_campus_availability pca ON pca.program_id = p.id AND pca.is_available = true
LEFT JOIN campuses c ON c.id = pca.campus_id
LEFT JOIN progressive_tuition pt ON pt.program_id = p.id
GROUP BY p.id, p.code, p.name, p.name_en, p.name_short, p.department, p.aliases, p.keywords, p.is_active;

-- Fast tuition lookup view
CREATE MATERIALIZED VIEW mv_tuition_summary AS
SELECT 
    p.code as program_code,
    p.name as program_name,
    c.code as campus_code,
    c.name as campus_name,
    c.foundation_fee,
    pt.tier_1_fee,
    pt.tier_2_fee,
    pt.tier_3_fee,
    (pt.tier_1_fee * 3 + pt.tier_2_fee * 3 + pt.tier_3_fee * 3) as total_9_semesters,
    (pt.tier_1_fee * 3 + pt.tier_2_fee * 3 + pt.tier_3_fee * 3 + c.foundation_fee) as total_with_foundation,
    c.discount_percentage,
    pt.year
FROM programs p
JOIN progressive_tuition pt ON pt.program_id = p.id
JOIN campuses c ON c.code = pt.campus_code
WHERE p.is_active = true AND c.is_active = true;

-- Create indexes on materialized views
CREATE INDEX idx_mv_program_search_code ON mv_program_search(code);
CREATE INDEX idx_mv_program_search_name ON mv_program_search USING gin(to_tsvector('english', name));
CREATE INDEX idx_mv_tuition_summary_lookup ON mv_tuition_summary(program_code, campus_code);

-- =====================================================
-- 11. SOFT DELETE SUPPORT
-- =====================================================

-- Add soft delete columns to main tables
ALTER TABLE programs ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL;
ALTER TABLE campuses ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL;
ALTER TABLE scholarships ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL;

-- Soft delete function
CREATE OR REPLACE FUNCTION soft_delete_program(program_code VARCHAR)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE programs 
    SET deleted_at = CURRENT_TIMESTAMP, is_active = false
    WHERE code = program_code AND deleted_at IS NULL;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 12. AUDIT TRAIL SYSTEM
-- =====================================================

CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    operation VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    changed_by VARCHAR(100),
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT
);

-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_logs (table_name, record_id, operation, new_values)
        VALUES (TG_TABLE_NAME, NEW.id, 'INSERT', row_to_json(NEW)::jsonb);
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_logs (table_name, record_id, operation, old_values, new_values)
        VALUES (TG_TABLE_NAME, NEW.id, 'UPDATE', row_to_json(OLD)::jsonb, row_to_json(NEW)::jsonb);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_logs (table_name, record_id, operation, old_values)
        VALUES (TG_TABLE_NAME, OLD.id, 'DELETE', row_to_json(OLD)::jsonb);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Apply audit triggers to main tables
CREATE TRIGGER audit_programs_trigger
    AFTER INSERT OR UPDATE OR DELETE ON programs
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_campuses_trigger
    AFTER INSERT OR UPDATE OR DELETE ON campuses
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- =====================================================
-- 13. CACHING & SESSION MANAGEMENT
-- =====================================================

CREATE TABLE cache_store (
    cache_key VARCHAR(255) PRIMARY KEY,
    cache_value JSONB NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_cache_store_expires ON cache_store(expires_at);

-- Cache management functions
CREATE OR REPLACE FUNCTION cache_set(key VARCHAR, value JSONB, ttl_minutes INTEGER DEFAULT 60)
RETURNS VOID AS $$
BEGIN
    INSERT INTO cache_store (cache_key, cache_value, expires_at)
    VALUES (key, value, CURRENT_TIMESTAMP + INTERVAL '1 minute' * ttl_minutes)
    ON CONFLICT (cache_key) 
    DO UPDATE SET 
        cache_value = EXCLUDED.cache_value,
        expires_at = EXCLUDED.expires_at;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION cache_get(key VARCHAR)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
BEGIN
    SELECT cache_value INTO result
    FROM cache_store
    WHERE cache_key = key AND expires_at > CURRENT_TIMESTAMP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 14. API RATE LIMITING
-- =====================================================

CREATE TABLE api_rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ip_address INET NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    request_count INTEGER DEFAULT 1,
    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_api_rate_limits_ip_endpoint ON api_rate_limits(ip_address, endpoint);
CREATE INDEX idx_api_rate_limits_window ON api_rate_limits(window_start);

-- Rate limiting function
CREATE OR REPLACE FUNCTION check_rate_limit(
    client_ip INET,
    api_endpoint VARCHAR,
    max_requests INTEGER DEFAULT 100,
    window_minutes INTEGER DEFAULT 60
) RETURNS BOOLEAN AS $$
DECLARE
    current_count INTEGER;
    window_start TIMESTAMP;
BEGIN
    window_start := CURRENT_TIMESTAMP - INTERVAL '1 minute' * window_minutes;
    
    SELECT COALESCE(SUM(request_count), 0) INTO current_count
    FROM api_rate_limits
    WHERE ip_address = client_ip 
    AND endpoint = api_endpoint
    AND window_start >= window_start;
    
    IF current_count >= max_requests THEN
        RETURN FALSE;
    END IF;
    
    INSERT INTO api_rate_limits (ip_address, endpoint)
    VALUES (client_ip, api_endpoint);
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 15. ENHANCED SEARCH WITH RANKING
-- =====================================================

-- Full-text search configuration
CREATE TEXT SEARCH CONFIGURATION fpt_search (COPY = english);

-- Enhanced search function with ranking
CREATE OR REPLACE FUNCTION search_programs_ranked(
    search_text VARCHAR,
    limit_count INTEGER DEFAULT 20
) RETURNS TABLE (
    code VARCHAR,
    name VARCHAR,
    name_en VARCHAR,
    department VARCHAR,
    match_type VARCHAR,
    rank_score REAL,
    available_campuses TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    WITH search_results AS (
        -- Exact code match (highest priority)
        SELECT 
            p.code, p.name, p.name_en, p.department,
            'exact_code'::VARCHAR as match_type,
            1.0::REAL as rank_score,
            ARRAY(SELECT c.code FROM campuses c 
                  JOIN program_campus_availability pca ON pca.campus_id = c.id 
                  WHERE pca.program_id = p.id AND pca.is_available = true) as campuses
        FROM programs p
        WHERE LOWER(p.code) = LOWER(search_text) AND p.deleted_at IS NULL
        
        UNION ALL
        
        -- Name similarity match
        SELECT 
            p.code, p.name, p.name_en, p.department,
            'name_similarity'::VARCHAR as match_type,
            similarity(p.name, search_text) as rank_score,
            ARRAY(SELECT c.code FROM campuses c 
                  JOIN program_campus_availability pca ON pca.campus_id = c.id 
                  WHERE pca.program_id = p.id AND pca.is_available = true) as campuses
        FROM programs p
        WHERE similarity(p.name, search_text) > 0.3 AND p.deleted_at IS NULL
        
        UNION ALL
        
        -- Alias match
        SELECT 
            p.code, p.name, p.name_en, p.department,
            'alias_match'::VARCHAR as match_type,
            0.8::REAL as rank_score,
            ARRAY(SELECT c.code FROM campuses c 
                  JOIN program_campus_availability pca ON pca.campus_id = c.id 
                  WHERE pca.program_id = p.id AND pca.is_available = true) as campuses
        FROM programs p
        WHERE EXISTS (
            SELECT 1 FROM unnest(p.aliases) as alias
            WHERE LOWER(alias) LIKE LOWER('%' || search_text || '%')
        ) AND p.deleted_at IS NULL
    )
    SELECT DISTINCT sr.code, sr.name, sr.name_en, sr.department, sr.match_type, sr.rank_score, sr.campuses
    FROM search_results sr
    ORDER BY sr.rank_score DESC, sr.name ASC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 16. PERFORMANCE MONITORING
-- =====================================================

CREATE TABLE query_performance_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_name VARCHAR(255) NOT NULL,
    execution_time_ms INTEGER NOT NULL,
    parameters JSONB,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_session VARCHAR(100)
);

CREATE INDEX idx_query_performance_name ON query_performance_log(query_name);
CREATE INDEX idx_query_performance_time ON query_performance_log(executed_at);

-- Performance monitoring function
CREATE OR REPLACE FUNCTION log_query_performance(
    p_query_name VARCHAR,
    p_start_time TIMESTAMP,
    p_parameters JSONB DEFAULT NULL,
    p_session VARCHAR DEFAULT NULL
) RETURNS VOID AS $$
DECLARE
    execution_ms INTEGER;
BEGIN
    execution_ms := EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - p_start_time)) * 1000;
    
    INSERT INTO query_performance_log (query_name, execution_time_ms, parameters, user_session)
    VALUES (p_query_name, execution_ms, p_parameters, p_session);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 17. REFRESH MATERIALIZED VIEWS FUNCTION
-- =====================================================

CREATE OR REPLACE FUNCTION refresh_search_views()
RETURNS VOID AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_program_search;
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_tuition_summary;
    
    -- Log the refresh
    INSERT INTO audit_logs (table_name, record_id, operation, new_values)
    VALUES ('materialized_views', gen_random_uuid(), 'REFRESH', 
            jsonb_build_object('refreshed_at', CURRENT_TIMESTAMP));
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 18. ADDITIONAL PERFORMANCE INDEXES
-- =====================================================

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_programs_active_department ON programs(is_active, department) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_tuition_year_active ON progressive_tuition(year, is_active);
CREATE INDEX IF NOT EXISTS idx_scholarships_year_percentage ON scholarships(year, percentage) WHERE is_active = true;

-- Partial indexes for better performance
CREATE INDEX IF NOT EXISTS idx_active_programs_only ON programs(code) WHERE is_active = true AND deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_available_program_campus ON program_campus_availability(program_id, campus_id) WHERE is_available = true;

-- =====================================================
-- 19. DATABASE MAINTENANCE FUNCTIONS
-- =====================================================

-- Clean up expired cache entries
CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_store WHERE expires_at < CURRENT_TIMESTAMP;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Clean up old audit logs (keep last 6 months)
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM audit_logs 
    WHERE changed_at < CURRENT_TIMESTAMP - INTERVAL '6 months';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Database health check function
CREATE OR REPLACE FUNCTION database_health_check()
RETURNS TABLE (
    metric VARCHAR,
    value VARCHAR,
    status VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'total_programs'::VARCHAR,
        COUNT(*)::VARCHAR,
        CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'WARNING' END::VARCHAR
    FROM programs WHERE deleted_at IS NULL
    
    UNION ALL
    
    SELECT 
        'active_campuses'::VARCHAR,
        COUNT(*)::VARCHAR,
        CASE WHEN COUNT(*) = 5 THEN 'OK' ELSE 'WARNING' END::VARCHAR
    FROM campuses WHERE is_active = true
    
    UNION ALL
    
    SELECT 
        'cache_entries'::VARCHAR,
        COUNT(*)::VARCHAR,
        'INFO'::VARCHAR
    FROM cache_store WHERE expires_at > CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql; 