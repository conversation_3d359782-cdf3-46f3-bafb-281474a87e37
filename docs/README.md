# FPT University Documentation

## 📁 Directory Structure

```
docs/
├── database/           # Database schemas and SQL files
│   └── fpt_university_2025_mvp.sql    # MVP database (production)
├── reference/          # Reference documentation
│   └── fpt_university_2025_reference.md    # Official FPT data reference
├── archive/            # Archived/deprecated files
│   └── old-versions/   # Previous versions not in use
└── README.md          # This file
```

## 🎯 Quick Start

### Database Setup
```bash
# Create database
createdb fpt_university

# Import MVP schema
psql -d fpt_university -f database/fpt_university_2025_mvp.sql
```

### Key Files

#### `database/fpt_university_2025_mvp.sql`
- **Purpose**: Production MVP database schema
- **Size**: ~300 lines (optimized)
- **Features**: 
  - 6 core tables
  - 2 essential views
  - 2 utility functions
  - Complete 2025 official data

#### `reference/fpt_university_2025_reference.md`
- **Purpose**: Official FPT University 2025 data reference
- **Contents**:
  - Tuition fees by campus/program
  - Scholarship information
  - Admission methods
  - Contact information

## 🚀 MVP Architecture

### Core Tables
1. `programs` - All study programs
2. `campuses` - 5 campus locations
3. `progressive_tuition` - 3-tier pricing
4. `scholarships` - 2800 scholarships
5. `admission_methods` - 4 methods
6. `program_campus_availability` - Program availability matrix

### Key Functions
```sql
-- Get tuition information
SELECT * FROM get_tuition_info('AI', 'HN', 2025);

-- Search programs
SELECT * FROM search_programs('phần mềm');
```

## 📝 Notes

- **Aliases**: Handled by RAG/AI layer instead of database
- **Caching**: Application-level caching recommended
- **Complexity**: Focused on education domain only
- **Performance**: Optimized for read-heavy workload

## 🔄 Version History

- **v1.0 MVP** (Current) - Simplified schema for production
- **v2.1** (Archived) - Enterprise version with advanced features 