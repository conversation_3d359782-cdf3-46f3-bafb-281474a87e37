{"name": "agent", "version": "1.0.0", "description": "", "scripts": {"test": "pnpx tsx tests/test-runner.ts", "test:unit": "pnpx tsx tests/test-runner.ts unit", "test:integration": "pnpx tsx tests/test-runner.ts integration", "test:performance": "pnpx tsx tests/test-runner.ts performance", "test:enhanced": "pnpx tsx tests/unit/intent-detection-enhanced.test.ts", "test:workflow": "pnpx tsx tests/integration/agent-workflow.test.ts", "test:benchmark": "pnpx tsx tests/performance/benchmark.test.ts", "test:all": "npm run test", "dev": "<PERSON>ra dev", "build": "mastra build", "ingest": "pnpx tsx src/scripts/ingest.ts", "index": "pnpx tsx src/scripts/indexing.ts", "index-intents": "pnpx tsx src/scripts/index-intents.ts", "query": "pnpx tsx src/scripts/query.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@ai-sdk/google": "^1.2.18", "@ai-sdk/openai": "^1.3.22", "@mastra/core": "^0.10.0", "@mastra/libsql": "^0.10.0", "@mastra/memory": "^0.10.0", "@mastra/openai": "1.0.1-alpha.48", "@mastra/rag": "^0.10.0", "dotenv": "16.5.0", "openai": "^4.103.0", "zod": "^3.25.28"}, "devDependencies": {"@types/node": "^22.15.21", "mastra": "^0.10.0", "typescript": "^5.8.3"}, "pnpm": {"onlyBuiltDependencies": ["esbuild", "onnxruntime-node"], "ignoredBuiltDependencies": ["protobufjs"]}}