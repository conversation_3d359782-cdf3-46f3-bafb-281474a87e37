{"typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.disableAutomaticTypeAcquisition": true, "typescript.tsserver.experimental.enableProjectDiagnostics": false, "typescript.tsserver.watchOptions": {"watchFile": "useFsEvents", "watchDirectory": "useFsEvents", "fallbackPolling": "dynamicPriorityPolling", "synchronousWatchDirectory": true, "excludeDirectories": ["**/node_modules", "**/bower_components", "**/dist"]}, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true}, "typescript.validate.enable": true, "javascript.validate.enable": true}